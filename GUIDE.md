# Comprehensive Guide for New Developers: Compliance Command Center DBOS

## Introduction

Welcome to the Compliance Command Center DBOS project! This guide will help you understand the project structure, set up your development environment, and get familiar with the key components of
this compliance management system built on the DBOS framework.

## System Overview

The Compliance Command Center is a comprehensive application that provides:
• Regulatory compliance monitoring
• KYC (Know Your Customer) processing
• Document analysis and management
• Compliance reporting and analytics

## Development Environment Setup

### Prerequisites

1. Install the following:
   • Node.js 18+ and npm/bun
   • PostgreSQL 12+ with UUID and pgcrypto extensions
   • Git

2. Clone the repository:
bash
git clone https://github.com/your-org/compliance-command-center-dbos.git
cd compliance-command-center-dbos


3. Install dependencies:
bash
npm install
# or
bun install


### Database Setup

1. Quick setup option:
bash
./quick_setup.sh


2. Manual setup:
bash
createdb dbos_kyc_demo
psql dbos_kyc_demo -f database_schema.sql


3. Configure environment:
bash
export DBOS_DATABASE_URL="postgresql://dbosadmin@localhost:5432/dbos_kyc_demo"


### Running the Application

You have several options for running the application:

1. Run frontend and backend together (recommended for development):
bash
npm run dev:full

This starts both the backend API server (port 3000) and frontend dev server (port 8080).

2. Run separately:
bash
# Terminal 1 - Start the backend API server
npm run dev:backend

# Terminal 2 - Start the frontend development server
npm run dev


3. Production mode:
bash
npm run start


Access points:
• Frontend: http://localhost:8080 (development)
• Backend API: http://localhost:3000
• Health check: http://localhost:3000/health

## Project Structure

compliance-command-center-dbos/
├── src/                  # Source code
│   ├── index.ts          # Main application entry point
│   ├── workflows/        # DBOS workflow definitions
│   ├── models/           # Data models
│   ├── services/         # Business logic services
│   └── utils/            # Utility functions
├── public/               # Static assets
├── migrations/           # Database migration files
├── scripts/              # Utility scripts
└── test/                 # Test files


## Key Components

### Database Schema

The application uses PostgreSQL with these key tables:
• compliance_documents: Stores documents for compliance checking
• compliance_rules: Defines compliance rules and patterns
• kyc_profiles: Stores KYC profiles with encrypted personal information
• compliance_violations: Records violations found during compliance checks
• regulatory_updates: Tracks regulatory changes and updates
• workflow_executions: Monitors DBOS workflow executions
• audit_logs: Maintains comprehensive audit trails

For detailed schema information, refer to DATABASE_SCHEMA.md.

### API Endpoints

The application provides RESTful API endpoints organized by functionality:
• Document management: /api/documents/*
• KYC processing: /api/kyc/*
• Compliance reporting: /api/reports/*
• Regulatory updates: /api/regulatory/*
• Dashboard analytics: /api/dashboard/*
• Workflow management: /api/workflows/*

### Frontend-Backend Integration

The application uses Vite for frontend development with automatic API proxying:
• Frontend Dev Server: Port 8080 (Vite)
• Backend API Server: Port 3000 (Express + DBOS)
• API Proxy: Vite automatically proxies /api/* requests to the backend

## Development Workflow

1. Understanding the codebase:
   • Start by exploring the main entry point (src/index.ts)
   • Review the workflow definitions in the workflows directory
   • Examine the database schema to understand data relationships

2. Making changes:
   • For backend changes, modify the relevant files in src/
   • For frontend changes, work in the frontend directories
   • Run tests to ensure your changes don't break existing functionality

3. Testing your changes:
bash
npm test
# or
bun test


For testing SQL syntax:
bash
npm run test-sql


## Current Project Status

The project is currently transitioning from static mock data to database-driven data. Refer to PHASE_2_IMPLEMENTATION_GUIDE.md for details on the remaining implementation tasks.

## Security Considerations

When working on this project, be aware of these security features:
• Personal information in KYC profiles is encrypted using pgcrypto
• Comprehensive audit logging for all critical operations
• Role-based access control for different user types
• Secure document handling and storage

## Troubleshooting

For common setup and configuration issues, refer to SETUP_TROUBLESHOOTING.md.

## Contributing Guidelines

1. Fork the repository
2. Create a feature branch (git checkout -b feature/amazing-feature)
3. Commit your changes (git commit -m 'Add some amazing feature')
4. Push to the branch (git push origin feature/amazing-feature)
5. Open a Pull Request

## Additional Resources

• Review the REFACTORING_SUMMARY.md for information about the transition from static mock data to database queries
• Check the performance considerations in the README for optimizing database operations
